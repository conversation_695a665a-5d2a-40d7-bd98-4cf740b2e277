#ifndef SIMPLEFILEDATASOURCE_H
#define SIMPLEFILEDATASOURCE_H

#include "idatasource.h"
#include <QTextCodec>
#include <QFileSystemWatcher>
#include <QTimer>

/**
 * @brief 简化的文件数据源
 * 
 * 重构后的文件数据源实现，特点：
 * - 移除了复杂的Worker线程，改为同步加载
 * - 保留了文件解析逻辑和编码支持
 * - 支持智能编码检测（UTF-8、GBK、GB2312等）
 * - 添加了文件大小限制保护
 * - 简化的错误处理机制
 * - 可选的文件监控功能
 */
class LOGVIEWER_EXPORT SimpleFileDataSource : public IDataSource
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit SimpleFileDataSource(QObject* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~SimpleFileDataSource() override;

    // ========== IDataSource接口实现 ==========
    
    /**
     * @brief 连接到文件数据源
     * @return 连接是否成功
     */
    bool connectToSource() override;

    /**
     * @brief 同步加载文件数据
     * @return 加载的日志条目列表
     */
    QVector<LogEntry> loadData() override;

    /**
     * @brief 断开连接
     */
    void disconnect() override;

    /**
     * @brief 检查是否已连接
     * @return 连接状态
     */
    bool isConnected() const override;

    /**
     * @brief 获取数据源信息
     * @return 数据源描述字符串
     */
    QString getSourceInfo() const override;

    // ========== 文件数据源特有方法 ==========
    
    /**
     * @brief 设置文件路径
     * @param filePath 文件路径
     */
    void setFilePath(const QString& filePath);

    /**
     * @brief 获取文件路径
     * @return 当前文件路径
     */
    QString getFilePath() const { return m_filePath; }

    /**
     * @brief 设置文件编码
     * @param encoding 编码名称（如"UTF-8", "GBK"等）
     */
    void setEncoding(const QString& encoding);

    /**
     * @brief 获取文件编码
     * @return 当前编码名称
     */
    QString getEncoding() const { return m_encoding; }

    /**
     * @brief 启用文件监控
     * @param enabled 是否启用监控
     */
    void setFileWatchingEnabled(bool enabled);

    /**
     * @brief 检查是否启用了文件监控
     * @return 监控状态
     */
    bool isFileWatchingEnabled() const { return m_fileWatchingEnabled; }

    /**
     * @brief 获取文件大小（字节）
     * @return 文件大小，-1表示文件不存在或无法访问
     */
    qint64 getFileSize() const;

    /**
     * @brief 获取文件行数估计
     * @return 估计的行数
     */
    int getEstimatedLineCount() const;

private slots:
    /**
     * @brief 处理文件变化
     * @param path 变化的文件路径
     */
    void onFileChanged(const QString& path);

private:
    // ========== 文件处理方法 ==========
    
    /**
     * @brief 检查文件是否可访问
     * @return 文件状态检查结果
     */
    bool checkFileAccess() const;

    /**
     * @brief 检查文件大小是否超限
     * @return 大小检查结果
     */
    bool checkFileSize() const;

    /**
     * @brief 智能检测文件编码
     * @param filePath 文件路径
     * @return 检测到的编码器，nullptr表示检测失败
     */
    QTextCodec* detectFileEncoding(const QString& filePath) const;

    /**
     * @brief 解析单行日志
     * @param line 日志行内容
     * @param lineNumber 行号
     * @return 解析后的日志条目
     */
    LogEntry parseLogLine(const QString& line, int lineNumber) const;

    /**
     * @brief 检测日志级别
     * @param text 文本内容
     * @return 检测到的日志级别
     */
    LogEntry::LogLevel detectLogLevel(const QString& text) const;

    /**
     * @brief 解析时间戳
     * @param text 文本内容
     * @return 解析到的时间戳，无效时返回当前时间
     */
    QDateTime parseTimestamp(const QString& text) const;

    /**
     * @brief 发出错误信号
     * @param error 错误描述
     */
    void emitError(const QString& error);

    /**
     * @brief 发出状态变化信号
     * @param status 状态描述
     */
    void emitStatusChanged(const QString& status);

private:
    // ========== 核心属性 ==========
    QString m_filePath;                     ///< 文件路径
    QString m_encoding;                     ///< 文件编码
    bool m_connected;                       ///< 连接状态
    bool m_fileWatchingEnabled;             ///< 文件监控启用状态
    
    // ========== 文件监控组件 ==========
    QFileSystemWatcher* m_fileWatcher;      ///< 文件系统监控器
    
    // ========== 缓存信息 ==========
    qint64 m_lastFileSize;                  ///< 上次文件大小
    QDateTime m_lastModified;               ///< 上次修改时间
    
    // ========== 常量定义 ==========
    static const qint64 MAX_FILE_SIZE_BYTES;    ///< 最大文件大小（字节）
    static const int MAX_LINES_TO_PROCESS;      ///< 最大处理行数
    static const int ENCODING_DETECTION_BYTES;  ///< 编码检测字节数
};

#endif // SIMPLEFILEDATASOURCE_H

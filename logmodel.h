#ifndef LOGMODEL_H
#define LOGMODEL_H

#include "logviewer_global.h"
#include "logentry.h"
//#include "configmanager.h"
#include <QAbstractTableModel>
#include <QVector>
#include <QMutex>

class LOGVIEWER_EXPORT LogModel : public QAbstractTableModel
{
    Q_OBJECT

public:
    enum Column
    {
        TimestampColumn = 0,
        LevelColumn,
        SourceColumn,
        MessageColumn,
        DetailsColumn,
        ColumnCount
    };

    explicit LogModel(QObject* parent = nullptr);
    ~LogModel() override;

    int      rowCount(const QModelIndex& parent = QModelIndex()) const override;
    int      columnCount(const QModelIndex& parent = QModelIndex()) const override;
    QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;

    void addLogEntry(const LogEntry& entry);
    void addLogEntries(const QVector< LogEntry >& entries);

    LogEntry            getLogEntry(int row) const;
    QVector< LogEntry > getEntries(int startIndex, int count) const;

    void clear();

    void setMaxEntries(uint64_t maxEntries);
    int  getTotalCount() const;

    void setColumnVisible(Column column, bool visible);
    bool isColumnVisible(Column column) const;

    int mapToActualColumn(int visibleColumn) const;


    // 删除指定范围的条目
    void removeEntriesAt(int startIndex, int count);
    // 保留指定范围的条目，删除其他条目
    void retainDataRange(int startIndex, int endIndex, int marginCount);

private:
    // 内部使用的删除实现
    void doRemoveEntries(int startIndex, int count, bool squeezeMemory = false);


signals:
    // 当内存使用接近上限时发出此信号
    void memoryWarning();

private:
    QVector< LogEntry > _entries;
    QVector< bool >     _columnVisibility;
    uint64_t            _maxEntries;
    mutable QMutex      _mutex;
};

#endif // LOGMODEL_H
